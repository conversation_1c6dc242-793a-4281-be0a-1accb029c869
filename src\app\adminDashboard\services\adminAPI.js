import axios from "axios";

const BASE_URL = "http://localhost:8080/api/admin";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    // Add any auth tokens if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export const adminAPI = {
  // User Management
  users: {
    // Create instructor
    createInstructor: (data) => apiClient.post("/instructors", data),

    // Create assistant
    createAssistant: (instructorId, data) =>
      apiClient.post(`/assistants/${instructorId}`, data),

    // Get all students
    getAllStudents: () => apiClient.get("/students"),

    // Get all instructors (we'll need to add this endpoint to backend)
    getAllInstructors: () => apiClient.get("/instructors"),

    // Search students by username
    searchStudents: (usernamePart) =>
      apiClient.post("/search", { usernamePart }),

    // Delete user
    deleteUser: (userId) => apiClient.delete(`/users/${userId}`),

    // Update instructor profile - Fixed endpoint path
    updateInstructorProfile: (instructorId, data) =>
      apiClient.put(`/instructors/${instructorId}/profile`, data),
  },

  // Course Management
  courses: {
    // Create course
    create: (instructorId, data) =>
      apiClient.post(`/courses/${instructorId}`, data),

    // Update course
    update: (courseId, data) => apiClient.put(`/courses/${courseId}`, data),

    // Delete course
    delete: (courseId) => apiClient.delete(`/courses/${courseId}`),

    // Get all courses (paginated)
    getAll: (page = 0, size = 100) =>
      apiClient.get(`/courses?page=${page}&size=${size}`),

    // Get courses by instructor (paginated)
    getByInstructor: (instructorId, page = 0, size = 100) =>
      apiClient.get(
        `/instructors/${instructorId}/courses?page=${page}&size=${size}`
      ),
  },

  // Lesson Management
  lessons: {
    // Create lesson
    create: (courseId, data) => apiClient.post(`/lessons/${courseId}`, data),

    // Update lesson
    update: (lessonId, data) => apiClient.put(`/lessons/${lessonId}`, data),

    // Delete lesson
    delete: (lessonId) => apiClient.delete(`/lessons/${lessonId}`),

    // Get lessons by course (paginated)
    getByCourse: (courseId, page = 0, size = 100) =>
      apiClient.get(`/courses/${courseId}/lessons?page=${page}&size=${size}`),

    // Generate access codes
    generateAccessCodes: (lessonId, count) =>
      apiClient.post(`/lessons/${lessonId}/generate-codes?count=${count}`),
  },

  // Exam Management
  exams: {
    // Create exam for a lesson
    create: (lessonId, examData) =>
      apiClient.post(`/exams/lessons/${lessonId}`, examData),

    // Get exam details
    getExam: (examId) => apiClient.get(`/exams/${examId}`),

    // Update exam
    update: (examData) => apiClient.put("/exams", examData),

    // Delete exam
    delete: (examId) => apiClient.delete(`/exams/${examId}`),

    // Get exam results
    getResults: (examId) => apiClient.get(`/exams/${examId}/results`),

    // Get all exams (when endpoint is available)
    getAll: (page = 0, size = 100) =>
      apiClient.get(`/exams?page=${page}&size=${size}`),

    // Get exams by lesson (when endpoint is available)
    getByLesson: (lessonId) => apiClient.get(`/exams/lessons/${lessonId}`),
  },

  // Assignment Management
  assignments: {
    // Create assignment for a lesson
    create: (lessonId, assignmentData) =>
      apiClient.post(`/assignments/lessons/${lessonId}`, assignmentData),

    // Get assignment details
    getAssignment: (assignmentId) =>
      apiClient.get(`/assignments/${assignmentId}`),

    // Update assignment
    update: (assignmentData) => apiClient.put("/assignments", assignmentData),

    // Delete assignment
    delete: (assignmentId) => apiClient.delete(`/assignments/${assignmentId}`),

    // Grade assignment submission
    grade: (submissionId, grade, feedback = "") =>
      apiClient.post(`/assignments/submissions/${submissionId}/grade`, null, {
        params: { grade, feedback },
      }),

    // Get all assignments (when endpoint is available)
    getAll: (page = 0, size = 100) =>
      apiClient.get(`/assignments?page=${page}&size=${size}`),

    // Get assignments by lesson (when endpoint is available)
    getByLesson: (lessonId) =>
      apiClient.get(`/assignments/lessons/${lessonId}`),

    // Get assignment submissions
    getSubmissions: (assignmentId) =>
      apiClient.get(`/assignments/${assignmentId}/submissions`),
  },

  // Access Code Management
  accessCodes: {
    // Get all access codes
    getAll: (page = 0, size = 10) =>
      apiClient.get(`/access-codes?page=${page}&size=${size}`),
  },

  // Statistics and Analytics
  analytics: {
    // Get dashboard stats
    getDashboardStats: async () => {
      try {
        const [studentsRes, instructorsRes, coursesRes] = await Promise.all([
          apiClient.get("/students"),
          apiClient.get("/instructors"),
          apiClient.get("/courses?page=0&size=1"), // Just get count
        ]);

        return {
          students: studentsRes.data?.length || 0,
          instructors: instructorsRes.data?.length || 0,
          courses:
            coursesRes.data?.totalElements ||
            coursesRes.data?.content?.length ||
            0,
          lessons: 0, // Will be calculated from courses if needed
          totalRevenue: 0, // Will be added when revenue tracking is implemented
          activeUsers: 0, // Will be added when user activity tracking is implemented
        };
      } catch (error) {
        throw error;
      }
    },
  },
};

// Helper function for handling API errors
export const handleAPIError = (error, defaultMessage = "An error occurred") => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.response?.data) {
    return error.response.data;
  }
  if (error.message) {
    return error.message;
  }
  return defaultMessage;
};

// Helper function for success responses
export const handleAPISuccess = (response) => {
  return response.data;
};
