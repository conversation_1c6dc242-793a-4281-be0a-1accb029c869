Stack trace:
Frame         Function      Args
0007FFFFA6C0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF95C0) msys-2.0.dll+0x1FEBA
0007FFFFA6C0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA998) msys-2.0.dll+0x67F9
0007FFFFA6C0  000210046832 (000210285FF9, 0007FFFFA578, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA6C0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA6C0  0002100690B4 (0007FFFFA6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA9A0  00021006A49D (0007FFFFA6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC94640000 ntdll.dll
7FFC58C00000 aswhook.dll
7FFC93F70000 KERNEL32.DLL
7FFC91790000 KERNELBASE.dll
7FFC92B70000 USER32.dll
7FFC91CC0000 win32u.dll
7FFC93720000 GDI32.dll
7FFC92120000 gdi32full.dll
7FFC91B80000 msvcp_win.dll
7FFC91DB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC93660000 advapi32.dll
7FFC92470000 msvcrt.dll
7FFC92810000 sechost.dll
7FFC92A50000 RPCRT4.dll
7FFC90D90000 CRYPTBASE.DLL
7FFC92080000 bcryptPrimitives.dll
7FFC93160000 IMM32.DLL
